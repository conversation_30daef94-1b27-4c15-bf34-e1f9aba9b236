#!/usr/bin/env node

/**
 * Security Test: File Permission Validation
 * This test verifies that users cannot access files belonging to other users
 */

import axios from 'axios';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const API_BASE = 'http://localhost:3000/api';

// Test users
const user1 = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  name: 'User One'
};

const user2 = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  name: 'User Two'
};

let user1Token = null;
let user2Token = null;
let user1FileId = null;
let user2FileId = null;

async function createTestImageFile() {
  // Create a simple 1x1 PNG image
  const pngData = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
    0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
    0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE,
    0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, 0x54,
    0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01,
    0xE2, 0x21, 0xBC, 0x33, 0x00, 0x00, 0x00, 0x00,
    0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
  ]);

  const testFilePath = path.join(__dirname, 'security-test.png');
  fs.writeFileSync(testFilePath, pngData);
  return testFilePath;
}

async function registerAndLoginUser(user, userLabel) {
  console.log(`👤 Setting up ${userLabel}...`);

  // Register user (might already exist)
  try {
    await axios.post(`${API_BASE}/auth/register`, user, { validateStatus: () => true });
  } catch (error) {
    // User might already exist, continue
  }

  // Login user
  const response = await axios.post(`${API_BASE}/auth/login`, {
    email: user.email,
    password: user.password
  });

  if (response.status === 200 && response.data.accessToken) {
    console.log(`✅ ${userLabel} logged in successfully`);
    return response.data.accessToken;
  } else {
    throw new Error(`Failed to login ${userLabel}`);
  }
}

async function uploadFileForUser(token, userLabel) {
  console.log(`📤 Uploading file for ${userLabel}...`);

  const testFilePath = await createTestImageFile();

  try {
    const form = new FormData();
    form.append('file', fs.createReadStream(testFilePath));
    form.append('isPublic', 'false');

    const response = await axios.post(`${API_BASE}/uploads/direct`, form, {
      headers: {
        ...form.getHeaders(),
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.status === 201) {
      console.log(`✅ File uploaded for ${userLabel}: ${response.data.id}`);
      fs.unlinkSync(testFilePath);
      return response.data.id;
    } else {
      throw new Error(`Upload failed for ${userLabel}`);
    }
  } catch (error) {
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }
    throw error;
  }
}

async function testCrossUserFileAccess() {
  console.log('\n🔒 Testing Cross-User File Access Security...\n');

  const tests = [
    {
      name: 'User1 tries to download User2\'s file',
      test: async () => {
        try {
          const response = await axios.get(`${API_BASE}/uploads/${user2FileId}/download`, {
            headers: { 'Authorization': `Bearer ${user1Token}` },
            validateStatus: () => true
          });

          if (response.status === 404) {
            console.log('✅ Correctly returned 404 (File not found)');
            return true;
          } else if (response.status === 400 && response.data.message === 'Access denied') {
            console.log('✅ Correctly returned 400 (Access denied)');
            return true;
          } else {
            console.log(`❌ Unexpected response: ${response.status} - ${JSON.stringify(response.data)}`);
            return false;
          }
        } catch (error) {
          console.log(`❌ Request failed: ${error.message}`);
          return false;
        }
      }
    },
    {
      name: 'User2 tries to download User1\'s file',
      test: async () => {
        try {
          const response = await axios.get(`${API_BASE}/uploads/${user1FileId}/download`, {
            headers: { 'Authorization': `Bearer ${user2Token}` },
            validateStatus: () => true
          });

          if (response.status === 404) {
            console.log('✅ Correctly returned 404 (File not found)');
            return true;
          } else if (response.status === 400 && response.data.message === 'Access denied') {
            console.log('✅ Correctly returned 400 (Access denied)');
            return true;
          } else {
            console.log(`❌ Unexpected response: ${response.status} - ${JSON.stringify(response.data)}`);
            return false;
          }
        } catch (error) {
          console.log(`❌ Request failed: ${error.message}`);
          return false;
        }
      }
    },
    {
      name: 'User1 tries to delete User2\'s file',
      test: async () => {
        try {
          const response = await axios.delete(`${API_BASE}/uploads/${user2FileId}`, {
            headers: { 'Authorization': `Bearer ${user1Token}` },
            validateStatus: () => true
          });

          if (response.status === 404) {
            console.log('✅ Correctly returned 404 (File not found)');
            return true;
          } else {
            console.log(`❌ Unexpected response: ${response.status} - ${JSON.stringify(response.data)}`);
            return false;
          }
        } catch (error) {
          console.log(`❌ Request failed: ${error.message}`);
          return false;
        }
      }
    },
    {
      name: 'User1 can access their own file',
      test: async () => {
        try {
          const response = await axios.get(`${API_BASE}/uploads/${user1FileId}/download`, {
            headers: { 'Authorization': `Bearer ${user1Token}` }
          });

          if (response.status === 200 && response.data.downloadUrl) {
            console.log('✅ User can access their own file');
            return true;
          } else {
            console.log(`❌ Failed to access own file: ${response.status}`);
            return false;
          }
        } catch (error) {
          console.log(`❌ Failed to access own file: ${error.message}`);
          return false;
        }
      }
    },
    {
      name: 'User2 can access their own file',
      test: async () => {
        try {
          const response = await axios.get(`${API_BASE}/uploads/${user2FileId}/download`, {
            headers: { 'Authorization': `Bearer ${user2Token}` }
          });

          if (response.status === 200 && response.data.downloadUrl) {
            console.log('✅ User can access their own file');
            return true;
          } else {
            console.log(`❌ Failed to access own file: ${response.status}`);
            return false;
          }
        } catch (error) {
          console.log(`❌ Failed to access own file: ${error.message}`);
          return false;
        }
      }
    }
  ];

  const results = [];

  for (const test of tests) {
    console.log(`\n--- ${test.name} ---`);
    const result = await test.test();
    results.push({ name: test.name, passed: result });
  }

  return results;
}

async function runSecurityTests() {
  console.log('🔐 Starting File Permission Security Tests\n');

  try {
    // Setup users
    user1Token = await registerAndLoginUser(user1, 'User1');
    user2Token = await registerAndLoginUser(user2, 'User2');

    // Upload files for each user
    user1FileId = await uploadFileForUser(user1Token, 'User1');
    user2FileId = await uploadFileForUser(user2Token, 'User2');

    // Test cross-user access
    const results = await testCrossUserFileAccess();

    console.log('\n📊 Security Test Results:');
    console.log('==========================');

    let passed = 0;
    let failed = 0;

    results.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.name}`);
      if (result.passed) passed++;
      else failed++;
    });

    console.log(`\nTotal: ${passed + failed} tests, ${passed} passed, ${failed} failed`);

    if (failed === 0) {
      console.log('\n🎉 All security tests passed! File permissions are properly enforced.');
    } else {
      console.log('\n⚠️  Some security tests failed. SECURITY VULNERABILITY DETECTED!');
    }

  } catch (error) {
    console.error('❌ Security test setup failed:', error.message);
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runSecurityTests().catch(console.error);
}

export { runSecurityTests };
