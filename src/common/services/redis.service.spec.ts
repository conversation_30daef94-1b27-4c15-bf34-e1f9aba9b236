import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { RedisService } from './redis.service.js';

// Mock the redis module
jest.mock('redis', () => ({
  createClient: jest.fn(() => ({
    on: jest.fn(),
    connect: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    setEx: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    hGet: jest.fn(),
    hSet: jest.fn(),
    hDel: jest.fn(),
    isReady: true,
  })),
}));

describe('RedisService', () => {
  let service: RedisService;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn((key: string, defaultValue?: any) => {
        switch (key) {
          case 'REDIS_HOST': return 'localhost';
          case 'REDIS_PORT': return 6379;
          case 'REDIS_PASSWORD': return undefined;
          default: return defaultValue;
        }
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RedisService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<RedisService>(RedisService);
    configService = module.get(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('isConnected', () => {
    it('should return connection status', () => {
      const result = service.isConnected();
      expect(typeof result).toBe('boolean');
    });
  });

  describe('getClient', () => {
    it('should return redis client or null', () => {
      const client = service.getClient();
      // Client might be null if not initialized
      expect(client === null || typeof client === 'object').toBe(true);
    });
  });
});
